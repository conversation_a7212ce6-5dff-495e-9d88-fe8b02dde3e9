/**
 * Enhanced Memory Monitor for Real-time Collaboration System
 * Provides detailed memory tracking and early warning system
 */

class MemoryMonitor {
  constructor(logger, config = {}) {
    this.logger = logger;
    this.config = {
      warningThreshold: config.warningThreshold || 0.75, // 75%
      criticalThreshold: config.criticalThreshold || 0.90, // 90%
      monitoringInterval: config.monitoringInterval || 30000, // 30 seconds
      gcThreshold: config.gcThreshold || 0.80, // 80% - trigger GC
      alertCooldown: config.alertCooldown || 300000, // 5 minutes between alerts
      ...config
    };

    this.lastAlert = 0;
    this.memoryHistory = [];
    this.maxHistorySize = 20; // Keep last 20 readings
    this.isMonitoring = false;
    this.monitoringInterval = null;
  }

  start() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.logger.info('Memory monitoring started', {
      warningThreshold: `${(this.config.warningThreshold * 100).toFixed(1)}%`,
      criticalThreshold: `${(this.config.criticalThreshold * 100).toFixed(1)}%`,
      interval: `${this.config.monitoringInterval / 1000}s`
    });

    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, this.config.monitoringInterval);

    // Initial check
    this.checkMemoryUsage();
  }

  stop() {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    this.logger.info('Memory monitoring stopped');
  }

  checkMemoryUsage() {
    const memUsage = process.memoryUsage();
    const heapUsagePercent = memUsage.heapUsed / memUsage.heapTotal;
    const rssUsageMB = memUsage.rss / 1024 / 1024;
    const heapUsedMB = memUsage.heapUsed / 1024 / 1024;
    const heapTotalMB = memUsage.heapTotal / 1024 / 1024;
    const externalMB = memUsage.external / 1024 / 1024;

    const memorySnapshot = {
      timestamp: Date.now(),
      heapUsagePercent,
      rssUsageMB,
      heapUsedMB,
      heapTotalMB,
      externalMB,
      raw: memUsage
    };

    // Add to history
    this.memoryHistory.push(memorySnapshot);
    if (this.memoryHistory.length > this.maxHistorySize) {
      this.memoryHistory.shift();
    }

    // Check thresholds and trigger actions
    this.evaluateMemoryStatus(memorySnapshot);

    return memorySnapshot;
  }

  evaluateMemoryStatus(snapshot) {
    const { heapUsagePercent, heapUsedMB, rssUsageMB } = snapshot;
    const now = Date.now();

    // Trigger garbage collection if needed
    if (heapUsagePercent > this.config.gcThreshold && global.gc) {
      this.logger.debug('Triggering garbage collection', {
        heapUsage: `${(heapUsagePercent * 100).toFixed(2)}%`,
        heapUsedMB: heapUsedMB.toFixed(2),
        threshold: `${(this.config.gcThreshold * 100).toFixed(1)}%`
      });
      global.gc();
    }

    // Check if we should send alerts (with cooldown)
    const shouldAlert = (now - this.lastAlert) > this.config.alertCooldown;

    if (heapUsagePercent > this.config.criticalThreshold) {
      if (shouldAlert) {
        this.logger.error('CRITICAL: Memory usage extremely high', {
          heapUsage: `${(heapUsagePercent * 100).toFixed(2)}%`,
          heapUsedMB: heapUsedMB.toFixed(2),
          rssUsageMB: rssUsageMB.toFixed(2),
          trend: this.getMemoryTrend(),
          recommendation: 'Consider restarting the service or reducing load'
        });
        this.lastAlert = now;
      }
    } else if (heapUsagePercent > this.config.warningThreshold) {
      if (shouldAlert) {
        this.logger.warn('WARNING: Memory usage high', {
          heapUsage: `${(heapUsagePercent * 100).toFixed(2)}%`,
          heapUsedMB: heapUsedMB.toFixed(2),
          rssUsageMB: rssUsageMB.toFixed(2),
          trend: this.getMemoryTrend(),
          recommendation: 'Monitor closely and consider cleanup'
        });
        this.lastAlert = now;
      }
    }
  }

  getMemoryTrend() {
    if (this.memoryHistory.length < 3) return 'insufficient_data';

    const recent = this.memoryHistory.slice(-3);
    const first = recent[0].heapUsagePercent;
    const last = recent[recent.length - 1].heapUsagePercent;
    const diff = last - first;

    if (diff > 0.05) return 'increasing';
    if (diff < -0.05) return 'decreasing';
    return 'stable';
  }

  getMemoryStats() {
    if (this.memoryHistory.length === 0) {
      return { status: 'no_data' };
    }

    const latest = this.memoryHistory[this.memoryHistory.length - 1];
    const trend = this.getMemoryTrend();
    
    // Calculate average over last 5 readings
    const recentReadings = this.memoryHistory.slice(-5);
    const avgHeapUsage = recentReadings.reduce((sum, reading) => 
      sum + reading.heapUsagePercent, 0) / recentReadings.length;

    return {
      current: {
        heapUsage: `${(latest.heapUsagePercent * 100).toFixed(2)}%`,
        heapUsedMB: latest.heapUsedMB.toFixed(2),
        rssUsageMB: latest.rssUsageMB.toFixed(2),
        externalMB: latest.externalMB.toFixed(2)
      },
      trend,
      average: {
        heapUsage: `${(avgHeapUsage * 100).toFixed(2)}%`
      },
      status: this.getMemoryStatus(latest.heapUsagePercent),
      history: this.memoryHistory.map(h => ({
        timestamp: h.timestamp,
        heapUsage: `${(h.heapUsagePercent * 100).toFixed(1)}%`
      }))
    };
  }

  getMemoryStatus(heapUsagePercent) {
    if (heapUsagePercent > this.config.criticalThreshold) return 'critical';
    if (heapUsagePercent > this.config.warningThreshold) return 'warning';
    return 'normal';
  }

  // Force immediate memory check and return detailed report
  getDetailedReport() {
    const snapshot = this.checkMemoryUsage();
    const stats = this.getMemoryStats();
    
    return {
      timestamp: snapshot.timestamp,
      ...stats,
      thresholds: {
        warning: `${(this.config.warningThreshold * 100).toFixed(1)}%`,
        critical: `${(this.config.criticalThreshold * 100).toFixed(1)}%`,
        gc: `${(this.config.gcThreshold * 100).toFixed(1)}%`
      },
      recommendations: this.getRecommendations(snapshot)
    };
  }

  getRecommendations(snapshot) {
    const recommendations = [];
    const { heapUsagePercent } = snapshot;

    if (heapUsagePercent > 0.9) {
      recommendations.push('URGENT: Consider immediate service restart');
      recommendations.push('Reduce active document count');
      recommendations.push('Check for memory leaks in document handling');
    } else if (heapUsagePercent > 0.8) {
      recommendations.push('Monitor memory usage closely');
      recommendations.push('Consider triggering document cleanup');
      recommendations.push('Review active connection count');
    } else if (heapUsagePercent > 0.7) {
      recommendations.push('Normal operation - monitor trends');
      recommendations.push('Consider enabling more aggressive GC if trend is increasing');
    }

    return recommendations;
  }

  // Emergency cleanup - force aggressive memory cleanup
  emergencyCleanup() {
    this.logger.warn('Performing emergency memory cleanup');
    
    // Clear memory history except latest
    if (this.memoryHistory.length > 1) {
      this.memoryHistory = this.memoryHistory.slice(-1);
    }

    // Force garbage collection multiple times
    if (global.gc) {
      for (let i = 0; i < 3; i++) {
        global.gc();
      }
    }

    const afterCleanup = this.checkMemoryUsage();
    this.logger.info('Emergency cleanup completed', {
      heapUsage: `${(afterCleanup.heapUsagePercent * 100).toFixed(2)}%`,
      heapUsedMB: afterCleanup.heapUsedMB.toFixed(2)
    });

    return afterCleanup;
  }
}

module.exports = MemoryMonitor;
